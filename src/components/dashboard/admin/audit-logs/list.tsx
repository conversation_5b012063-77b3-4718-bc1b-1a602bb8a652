'use client';

import { useState } from 'react';
import { 
  RefreshCw, 
  Search, 
  Eye, 
  AlertTriangle, 
  Clock, 
  User, 
  Globe,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AuditLogResponse, 
  formatProcessingTime, 
  formatBytes, 
  getStatusColor, 
  getStatusBadgeVariant, 
  getMethodColor,
  parseJsonSafely
} from '@/lib/schemas/audit-logs';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

interface AuditLogsListProps {
  readonly logs: AuditLogResponse[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function AuditLogsList({ logs, isLoading, error, onRefresh }: AuditLogsListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLog, setSelectedLog] = useState<AuditLogResponse | null>(null);
  const [sortField, setSortField] = useState<keyof AuditLogResponse>('requestTimestamp');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Filter logs based on search query
  const filteredLogs = logs.filter(log => 
    log.endpointUrl.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.httpMethod.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.userEmail?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.clientIpAddress.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.responseStatusCode.toString().includes(searchQuery)
  );

  // Sort logs
  const sortedLogs = [...filteredLogs].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (aValue === undefined || aValue === null) return 1;
    if (bValue === undefined || bValue === null) return -1;
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    return 0;
  });

  const handleSort = (field: keyof AuditLogResponse) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const formatDate = (timestamp: string | number) => {
      try {
        let date: Date;
        let milliseconds = 0;

        if (typeof timestamp === 'string') {
          // If it's a string, try to parse it as ISO string first, then as epoch
          if (timestamp.includes('T') || timestamp.includes('-')) {
            date = new Date(timestamp);
          } else {
            // Assume it's epoch time in seconds with decimal precision
            const timestampFloat = parseFloat(timestamp);
            date = new Date(timestampFloat * 1000);
            // Extract milliseconds from the decimal part
            milliseconds = Math.floor((timestampFloat % 1) * 1000);
          }
        } else {
          // Convert seconds to milliseconds by multiplying by 1000
          date = new Date(timestamp * 1000);
          // Extract milliseconds from the decimal part
          milliseconds = Math.floor((timestamp % 1) * 1000);
        }

        // Format with seconds and add milliseconds manually
        const formattedDate = format(date, 'dd MMM yyyy HH:mm:ss', { locale: tr });
        return `${formattedDate}.${milliseconds.toString().padStart(3, '0')}`;
      } catch (error) {
        console.error('Error formatting date:', error);
        return String(timestamp);
      }
    };

  if (error) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-semibold mb-2">Hata Oluştu</h3>
          <p className="text-muted-foreground text-center mb-4">{error}</p>
          <Button onClick={onRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Tekrar Dene
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Controls */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Endpoint, method, kullanıcı veya IP ara..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {filteredLogs.length} kayıt
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
            className="gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
        </div>
      </div>

      {/* Desktop Table View */}
      <div className="hidden md:block border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('requestTimestamp')}
              >
                <div className="flex items-center gap-2">
                  Zaman
                  {sortField === 'requestTimestamp' && (
                    sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('httpMethod')}
              >
                <div className="flex items-center gap-2">
                  Method
                  {sortField === 'httpMethod' && (
                    sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('endpointUrl')}
              >
                <div className="flex items-center gap-2">
                  Endpoint
                  {sortField === 'endpointUrl' && (
                    sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('responseStatusCode')}
              >
                <div className="flex items-center gap-2">
                  Status
                  {sortField === 'responseStatusCode' && (
                    sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('processingTimeMs')}
              >
                <div className="flex items-center gap-2">
                  Süre
                  {sortField === 'processingTimeMs' && (
                    sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead>Kullanıcı</TableHead>
              <TableHead>IP</TableHead>
              <TableHead>İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                </TableRow>
              ))
            ) : sortedLogs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <AlertTriangle className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">
                      {searchQuery ? 'Arama kriterlerine uygun kayıt bulunamadı' : 'Henüz denetim kaydı bulunmuyor'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              sortedLogs.map((log) => (
                <TableRow key={log.id} className="hover:bg-muted/50">
                  <TableCell className="text-sm">
                    {formatDate(log.requestTimestamp)}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getMethodColor(log.httpMethod)}>
                      {log.httpMethod}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-mono text-sm max-w-xs truncate">
                    {log.endpointUrl}
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(log.responseStatusCode)}>
                      {log.responseStatusCode}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatProcessingTime(log.processingTimeMs)}
                    </div>
                  </TableCell>
                  <TableCell className="text-sm">
                    {log.userEmail ? (
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="truncate max-w-24">{log.userEmail}</span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-sm font-mono">
                    <div className="flex items-center gap-1">
                      <Globe className="h-3 w-3" />
                      {log.clientIpAddress}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedLog(log)}
                      className="gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      Detay
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4">
        {isLoading ? (
          Array.from({ length: 3 }).map((_, index) => (
            <Card key={index}>
              <CardContent className="p-4 space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
          ))
        ) : sortedLogs.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <AlertTriangle className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-muted-foreground text-center">
                {searchQuery ? 'Arama kriterlerine uygun kayıt bulunamadı' : 'Henüz denetim kaydı bulunmuyor'}
              </p>
            </CardContent>
          </Card>
        ) : (
          sortedLogs.map((log) => (
            <Card key={log.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={getMethodColor(log.httpMethod)}>
                      {log.httpMethod}
                    </Badge>
                    <Badge variant={getStatusBadgeVariant(log.responseStatusCode)}>
                      {log.responseStatusCode}
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedLog(log)}
                    className="gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    Detay
                  </Button>
                </div>
                
                <div className="space-y-2">
                  <p className="font-mono text-sm break-all">{log.endpointUrl}</p>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatProcessingTime(log.processingTimeMs)}
                    </div>
                    {log.userEmail && (
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="truncate">{log.userEmail}</span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Globe className="h-3 w-3" />
                      {log.clientIpAddress}
                    </div>
                    <span>{formatDate(log.requestTimestamp)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Detail Dialog */}
      <Dialog open={!!selectedLog} onOpenChange={() => setSelectedLog(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Denetim Kaydı Detayları</DialogTitle>
            <DialogDescription>
              ID: {selectedLog?.id} - {selectedLog && formatDate(selectedLog.requestTimestamp)}
            </DialogDescription>
          </DialogHeader>
          
          {selectedLog && (
            <ScrollArea className="max-h-[700vh] max-w">
              <div className="space-y-6">
                {/* Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Temel Bilgiler</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Method:</span>
                        <Badge variant="outline" className={getMethodColor(selectedLog.httpMethod)}>
                          {selectedLog.httpMethod}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Status:</span>
                        <Badge variant={getStatusBadgeVariant(selectedLog.responseStatusCode)}>
                          {selectedLog.responseStatusCode}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">İşlem Süresi:</span>
                        <span>{formatProcessingTime(selectedLog.processingTimeMs)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">İstek Boyutu:</span>
                        <span>{formatBytes(selectedLog.requestBodySize)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Yanıt Boyutu:</span>
                        <span>{formatBytes(selectedLog.responseBodySize)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">E-posta:</span>
                        <span>{selectedLog.userEmail || '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">E-posta:</span>
                        <span>{selectedLog.userEmail || '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Roller:</span>
                        <span>{selectedLog.userRoles || '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">IP Adresi:</span>
                        <span className="font-mono">{selectedLog.clientIpAddress}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Oturum ID:</span>
                        <span className="font-mono">{selectedLog.sessionId || '-'}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Kullanıcı Bilgileri</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">E-posta:</span>
                        <span>{selectedLog.userEmail || '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Roller:</span>
                        <span>{selectedLog.userRoles || '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">IP Adresi:</span>
                        <span className="font-mono">{selectedLog.clientIpAddress}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Oturum ID:</span>
                        <span className="font-mono">{selectedLog.sessionId || '-'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Endpoint and Parameters */}
                <div>
                  <h4 className="font-semibold mb-2">Endpoint ve Parametreler</h4>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm text-muted-foreground">URL:</span>
                      <p className="font-mono text-sm bg-muted p-2 rounded break-all">
                        {selectedLog.endpointUrl}
                      </p>
                    </div>
                    
                    {selectedLog.queryParameters && (
                      <div>
                        <span className="text-sm text-muted-foreground">Query Parametreleri:</span>
                        <pre className="text-sm bg-muted p-2 rounded overflow-auto">
                          {JSON.stringify(parseJsonSafely(selectedLog.queryParameters), null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {selectedLog.pathParameters && (
                      <div>
                        <span className="text-sm text-muted-foreground">Path Parametreleri:</span>
                        <pre className="text-sm bg-muted p-2 rounded overflow-auto">
                          {JSON.stringify(parseJsonSafely(selectedLog.pathParameters), null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>

                {/* Timestamps */}
                <div>
                  <h4 className="font-semibold mb-2">Zaman Bilgileri</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">İstek Zamanı:</span>
                      <span>{formatDate(selectedLog.requestTimestamp)}</span>
                      <span className="text-muted-foreground">Yanıt Zamanı:</span>
                      <span>{formatDate(selectedLog.responseTimestamp)}</span>
                    </div>
                  </div>
                </div>

                {/* User Agent */}
                {selectedLog.userAgent && (
                  <div>
                    <h4 className="font-semibold mb-2">User Agent</h4>
                    <p className="text-sm bg-muted p-2 rounded break-all">
                      {selectedLog.userAgent}
                    </p>
                  </div>
                )}

                {/* Error Message */}
                {selectedLog.errorMessage && (
                  <div>
                    <h4 className="font-semibold mb-2 text-destructive">Hata Mesajı</h4>
                    <p className="text-sm bg-destructive/10 text-destructive p-2 rounded">
                      {selectedLog.errorMessage}
                    </p>
                  </div>
                )}

                {/* Request Headers */}
                {selectedLog.requestHeaders && (
                  <div>
                    <h4 className="font-semibold mb-2">İstek Headers</h4>
                    <pre className="text-sm bg-muted p-2 rounded overflow-auto">
                      {JSON.stringify(parseJsonSafely(selectedLog.requestHeaders), null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
