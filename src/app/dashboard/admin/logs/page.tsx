'use client';

import { useState, useEffect, useMemo } from 'react';
import { FileText, Download, RefreshCw, Search, Eye, AlertTriangle } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BackButton } from '@/components/ui/back-button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { serverLogsService } from '@/lib/api';

export default function ServerLogsPage() {
  const [logContent, setLogContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('viewer');
  const [visibleLines, setVisibleLines] = useState(10); // Start with last 10 lines
  const [loadingMore, setLoadingMore] = useState(false);

  // Load log content
  const loadLogContent = async () => {
    try {
      setLoading(true);
      setError(null);
      const content = await serverLogsService.getServerLogContent();
      setLogContent(content);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Log içeriği yüklenirken hata oluştu';
      setError(errorMessage);
      console.error('Load log content error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Download log file
  const handleDownload = async () => {
    try {
      setDownloading(true);
      const blob = await serverLogsService.downloadServerLog();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `server-${new Date().toISOString().split('T')[0]}-${new Date().toTimeString().split(' ')[0].replace(/:/g, '')}.log`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Log dosyası başarıyla indirildi');
    } catch (error) {
      console.error('Download error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Log dosyası indirilemedi';
      toast.error(errorMessage);
    } finally {
      setDownloading(false);
    }
  };

  // Handle retry
  const handleRetry = () => {
    loadLogContent();
  };

  // Load initial data
  useEffect(() => {
    loadLogContent();
  }, []);

  // Process log content for display with syntax highlighting
  const processedLogContent = useMemo(() => {
    if (!logContent) return [];

    const lines = logContent.split('\n');
    return lines.map((line, index) => {
      let processedLine = line;

      // Color specific log levels
      const logLevelPatterns = [
        { pattern: /\b(DEBUG)\b/gi, className: 'text-blue-600 dark:text-blue-400' },
        { pattern: /\b(ERROR)\b/gi, className: 'text-red-600 dark:text-red-400 font-semibold' },
        { pattern: /\b(WARN|WARNING)\b/gi, className: 'text-yellow-600 dark:text-yellow-400 font-medium' },
        { pattern: /\b(TRACE)\b/gi, className: 'text-purple-600 dark:text-purple-400' },
        { pattern: /\b(INFO)\b/gi, className: 'text-green-600 dark:text-green-400' },
      ];

      // Apply highlighting by wrapping matches in spans
      logLevelPatterns.forEach(({ pattern, className }) => {
        processedLine = processedLine.replace(pattern, `<span class="${className}">$1</span>`);
      });

      return {
        lineNumber: index + 1,
        content: processedLine,
        originalContent: line,
      };
    });
  }, [logContent]);

  // Filter log content based on search query
  const filteredLogContent = useMemo(() => {
    if (!searchQuery.trim()) return processedLogContent;

    const query = searchQuery.toLowerCase();
    return processedLogContent.filter(line =>
      line.originalContent.toLowerCase().includes(query)
    );
  }, [processedLogContent, searchQuery]);

  // Get visible log content (last N lines, newest first)
  const visibleLogContent = useMemo(() => {
    if (filteredLogContent.length === 0) return [];

    // When searching, show all results
    if (searchQuery.trim()) {
      return filteredLogContent;
    }

    // For normal view, show last N lines (newest first)
    const totalLines = filteredLogContent.length;
    const startIndex = Math.max(0, totalLines - visibleLines);
    return filteredLogContent.slice(startIndex).reverse();
  }, [filteredLogContent, visibleLines, searchQuery]);

  // Load more lines function
  const loadMoreLines = async () => {
    setLoadingMore(true);
    // Simulate a small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));
    setVisibleLines(prev => prev + 20);
    setLoadingMore(false);
  };

  // Reset visible lines when search changes
  useEffect(() => {
    if (!searchQuery.trim()) {
      setVisibleLines(10);
    }
  }, [searchQuery]);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Back Button */}
        <BackButton />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <FileText className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Sunucu Log Yönetimi</h1>
              <p className="text-muted-foreground">
                Sunucu log dosyalarını görüntüle ve indir
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
              disabled={downloading}
              className="gap-2"
            >
              <Download className={`h-4 w-4 ${downloading ? 'animate-pulse' : ''}`} />
              {downloading ? 'İndiriliyor...' : 'Log İndir'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadLogContent}
              disabled={loading}
              className="gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-1 lg:w-[400px]">
            <TabsTrigger value="viewer" className="gap-2">
              <Eye className="h-4 w-4" />
              Log Görüntüleyici
            </TabsTrigger>
          </TabsList>

          <TabsContent value="viewer" className="space-y-6">
            {/* Search */}
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Log Arama</CardTitle>
                <CardDescription>
                  Log içeriğinde belirli metinleri arayın
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Log içeriğinde ara..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                    disabled={loading}
                  />
                </div>
                {searchQuery && (
                  <p className="text-sm text-muted-foreground mt-2">
                    {filteredLogContent.length} satır bulundu
                  </p>
                )}
                {!searchQuery && logContent && (
                  <p className="text-sm text-muted-foreground mt-2">
                    {visibleLines} / {processedLogContent.length} satır gösteriliyor
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Log Content */}
            <Card>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">Log İçeriği</CardTitle>
                    <CardDescription>
                      Sunucu log dosyasının içeriği
                    </CardDescription>
                  </div>
                  {logContent && (
                    <div className="text-sm text-muted-foreground">
                      {searchQuery ?
                        `${filteredLogContent.length} satır bulundu` :
                        `${visibleLines} / ${processedLogContent.length} satır gösteriliyor`
                      }
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-2">
                    {[...Array(10)].map((_, i) => (
                      <div key={i} className="flex items-center gap-4">
                        <Skeleton className="h-4 w-12" />
                        <Skeleton className="h-4 flex-1" />
                      </div>
                    ))}
                  </div>
                ) : error ? (
                  <div className="text-center py-8">
                    <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <Button onClick={handleRetry} variant="outline">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Tekrar Dene
                    </Button>
                  </div>
                ) : logContent ? (
                  <div className="space-y-4">
                    <ScrollArea className="h-[600px] w-full border rounded-md">
                      <div className="p-4 font-mono text-sm">
                        {visibleLogContent.length === 0 ? (
                          <div className="text-center py-8 text-muted-foreground">
                            {searchQuery ? 'Arama kriterinize uygun log satırı bulunamadı' : 'Log içeriği bulunamadı'}
                          </div>
                        ) : (
                          visibleLogContent.map((line) => (
                            <div
                              key={line.lineNumber}
                              className="flex gap-4 py-1 hover:bg-muted/50 group"
                            >
                              <span className="text-muted-foreground text-xs w-12 flex-shrink-0 text-right select-none">
                                {line.lineNumber}
                              </span>
                              <span
                                className="flex-1 break-words"
                                style={{ wordWrap: 'break-word' }}
                                dangerouslySetInnerHTML={{ __html: line.content }}
                              />
                            </div>
                          ))
                        )}
                      </div>
                    </ScrollArea>

                    {/* Show More Button */}
                    {!searchQuery && visibleLines < processedLogContent.length && (
                      <div className="flex justify-center">
                        <Button
                          onClick={loadMoreLines}
                          disabled={loadingMore}
                          variant="outline"
                          className="gap-2"
                        >
                          {loadingMore ? (
                            <>
                              <RefreshCw className="h-4 w-4 animate-spin" />
                              Yükleniyor...
                            </>
                          ) : (
                            <>
                              <FileText className="h-4 w-4" />
                              20 Satır Daha Göster
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    Log içeriği bulunamadı
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
